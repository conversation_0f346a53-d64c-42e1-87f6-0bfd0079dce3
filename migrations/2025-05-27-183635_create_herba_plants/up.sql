-- Create herba_plants table for botanical plant database
CREATE TABLE herba_plants (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    latin_name TEXT NOT NULL UNIQUE,
    common_name TEXT NOT NULL,
    family TEXT,
    genus TEXT,
    species TEXT,
    variety TEXT,
    description TEXT,
    growth_habit TEXT, -- annual, perennial, biennial
    mature_height TEXT,
    mature_width TEXT,
    sun_requirements TEXT, -- full sun, partial shade, full shade
    water_requirements TEXT, -- low, medium, high
    soil_type TEXT,
    soil_ph TEXT,
    hardiness_zone TEXT,
    bloom_time TEXT,
    bloom_color TEXT,
    foliage_color TEXT,
    fruit_time TEXT,
    companion_plants TEXT, -- JSON array of compatible plant IDs
    antagonist_plants TEXT, -- JSON array of incompatible plant IDs
    pest_resistance TEXT,
    disease_resistance TEXT,
    edible_parts TEXT, -- leaves, fruits, roots, flowers, etc.
    medicinal_uses TEXT,
    culinary_uses TEXT,
    propagation_methods TEXT, -- seed, cutting, division, etc.
    seed_germination_time INTEGER, -- days
    seed_viability INTEGER, -- years
    days_to_maturity INTEGER,
    harvest_method TEXT,
    storage_method TEXT,
    nutritional_info TEXT, -- JSON with vitamins, minerals, etc.
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create index on latin_name for fast lookups
CREATE INDEX idx_herba_plants_latin_name ON herba_plants(latin_name);

-- Create index on common_name for search
CREATE INDEX idx_herba_plants_common_name ON herba_plants(common_name);

-- Create index on growth_habit for filtering
CREATE INDEX idx_herba_plants_growth_habit ON herba_plants(growth_habit);

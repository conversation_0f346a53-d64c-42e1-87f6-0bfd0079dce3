-- Remove herba_plant_id from plants table
DROP INDEX IF EXISTS idx_plants_herba_plant_id;

-- SQLite doesn't support dropping columns directly, so we need to recreate the table
-- This is a simplified approach - in production you'd want to preserve data
CREATE TABLE plants_backup AS SELECT
    id, name, description, latin_name, variety, note,
    nutrient_consumption, nutrient_deposit, lighting, temperature,
    light_amount, sowing_time, propagation_time, harvest_time,
    growth_duration, harvest_duration, field_id
FROM plants;

DROP TABLE plants;

CREATE TABLE plants (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    latin_name TEXT,
    variety TEXT,
    note TEXT,
    nutrient_consumption TEXT,
    nutrient_deposit TEXT,
    lighting TEXT,
    temperature TEXT,
    light_amount TEXT,
    sowing_time TEXT,
    propagation_time TEXT,
    harvest_time TEXT,
    growth_duration TEXT,
    harvest_duration TEXT,
    field_id INTEGER
);

INSERT INTO plants SELECT * FROM plants_backup;
DROP TABLE plants_backup;

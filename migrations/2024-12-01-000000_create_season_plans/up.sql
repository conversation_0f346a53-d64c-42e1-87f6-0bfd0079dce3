CREATE TABLE IF NOT EXISTS season_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    season_id INTEGER NOT NULL,
    property_id INTEGER NOT NULL,
    growing_area_id INTEGER,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (season_id) REFERENCES seasons(id),
    FOREIGN KEY (property_id) REFERENCES properties(id),
    FOREIGN KEY (growing_area_id) REFERENCES growing_areas(id)
);

CREATE TABLE IF NOT EXISTS season_plan_plants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER NOT NULL,
    plant_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    position_x INTEGER,
    position_y INTEGER,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES season_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (plant_id) REFERENCES plants(id)
);

CREATE INDEX idx_season_plans_season_id ON season_plans(season_id);
CREATE INDEX idx_season_plans_property_id ON season_plans(property_id);
CREATE INDEX idx_season_plans_growing_area_id ON season_plans(growing_area_id);
CREATE INDEX idx_season_plan_plants_plan_id ON season_plan_plants(plan_id);
CREATE INDEX idx_season_plan_plants_plant_id ON season_plan_plants(plant_id);

[package]
name = "garden_planner_web"
version = "0.1.0"
edition = "2021"

[lib]
name = "garden_planner_web"
path = "src/lib.rs"

[[bin]]
name = "garden_planner_web"
path = "src/main.rs"

[dependencies]
actix-web = "4"
actix-session = { version = "0.7", features = ["cookie-session"] }
actix-files = "0.6"
bcrypt = "0.12"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
diesel = { version = "2.0", features = ["sqlite", "r2d2", "chrono", "64-column-tables"] }
r2d2 = "0.8"
dotenv = "0.15"
env_logger = "0.10"
once_cell = "1.17"
tera = "1.17"
log = "0.4.22"
rand = "0.8"
argon2 = "0.5"
rand_core = "0.6"
chrono = { version = "0.4", features = ["serde"] }
lazy_static = "1.5.0"
tokio = "1.41.1"
cron = "0.9"
regex = "1.10.3"
futures = "0.3.30"
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }
scraper = "0.17"
url = "2.4"
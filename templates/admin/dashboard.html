{% extends "base.html" %}
{% block title %}Admin Dashboard{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">Admin Dashboard</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- User Management -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">User Management</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Manage users, roles, and permissions</p>
            <a href="/admin/users" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Manage Users
            </a>
        </div>

        <!-- System Statistics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">System Statistics</h2>
            <div class="space-y-2">
                <p class="text-gray-600 dark:text-gray-300">Total Users: <span class="font-semibold">{{ total_users | default(value="N/A") }}</span></p>
                <p class="text-gray-600 dark:text-gray-300">Total Plants: <span class="font-semibold">{{ total_plants | default(value="N/A") }}</span></p>
                <p class="text-gray-600 dark:text-gray-300">Total Properties: <span class="font-semibold">{{ total_properties | default(value="N/A") }}</span></p>
            </div>
        </div>

        <!-- System Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">System Settings</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Configure system-wide settings</p>
            <a href="/admin/settings" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                Settings
            </a>
        </div>

        <!-- Database Management -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Database Management</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Backup, restore, and maintain database</p>
            <a href="/admin/database" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                Database Tools
            </a>
        </div>

        <!-- Herba Integration -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Herba Integration</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Manage plant data synchronization</p>
            <a href="/admin/herba" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                Herba Tools
            </a>
        </div>

        <!-- Notifications -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Notification System</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Monitor and manage notifications</p>
            <a href="/admin/notifications" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                Notifications
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>
        <div class="text-gray-600 dark:text-gray-300">
            <p>Recent activity logs will be displayed here...</p>
        </div>
    </div>
</div>
{% endblock %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Gardening App{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/wizard.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <script src="/static/js/scripts.js" defer></script>
    {% block head %}{% endblock %}
</head>
<body class="bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100">
<nav class="bg-green-800 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
        <a href="/" class="text-xl font-bold">Garden Planner</a>
        <div class="space-x-4">
            <a href="/plants" class="hover:text-green-200">Plants</a>
            <a href="/seeds" class="hover:text-green-200">Seeds</a>
            <a href="/plots" class="hover:text-green-200">Plots</a>
            <a href="/property" class="hover:text-green-200">Properties</a>
            <a href="/seasons" class="hover:text-green-200">Seasons</a>
            <a href="/season_plans" class="hover:text-green-200">Season Plans</a>
            <a href="/notifications" class="hover:text-green-200">Notifications</a>
            {% if user %}
                <a href="/auth/logout" class="hover:text-green-200">Logout</a>
            {% else %}
                <a href="/auth/login" class="hover:text-green-200">Login</a>
            {% endif %}
        </div>
    </div>
</nav>
<main class="container mx-auto px-4 py-6">
    {% block content %}{% endblock %}
</main>
</body>
</html>

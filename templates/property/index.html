{% extends "base.html" %}

{% block title %}My Properties{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">My Properties</h1>
                <div>
                    <a href="/property/new" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Property
                    </a>
                    <a href="/property/wizard" class="btn btn-success">
                        <i class="fas fa-magic"></i> Property Wizard
                    </a>
                </div>
            </div>

            {% if properties %}
                <div class="row">
                    {% for property in properties %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card shadow h-100">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">{{ property.name }}</h6>
                                    <div class="dropdown no-arrow">
                                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink{{ property.id }}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink{{ property.id }}">
                                            <a class="dropdown-item" href="/property/{{ property.id }}/view">
                                                <i class="fas fa-eye fa-sm fa-fw mr-2 text-gray-400"></i>
                                                View
                                            </a>
                                            <a class="dropdown-item" href="/property/{{ property.id }}/edit">
                                                <i class="fas fa-edit fa-sm fa-fw mr-2 text-gray-400"></i>
                                                Edit
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item text-danger" href="#" onclick="deleteProperty({{ property.id }})">
                                                <i class="fas fa-trash fa-sm fa-fw mr-2 text-gray-400"></i>
                                                Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Floors
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ property.floors }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-building fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                    
                                    {% if property.outside_area %}
                                        <div class="row no-gutters align-items-center mt-3">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    Outside Area
                                                </div>
                                                <div class="h6 mb-0 font-weight-bold text-gray-800">{{ property.outside_area }} m²</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-tree fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    {% endif %}
                                    
                                    {% if property.inside_area %}
                                        <div class="row no-gutters align-items-center mt-3">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                    Inside Area
                                                </div>
                                                <div class="h6 mb-0 font-weight-bold text-gray-800">{{ property.inside_area }} m²</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-home fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="card-footer">
                                    <a href="/property/{{ property.id }}/view" class="btn btn-primary btn-sm btn-block">
                                        <i class="fas fa-eye"></i> View Property
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="card shadow mb-4">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-home fa-5x text-gray-300"></i>
                        </div>
                        <h4 class="text-gray-600 mb-3">No Properties Yet</h4>
                        <p class="text-gray-500 mb-4">
                            Start by creating your first property to begin planning your garden.
                        </p>
                        <div>
                            <a href="/property/wizard" class="btn btn-success btn-lg mr-3">
                                <i class="fas fa-magic"></i> Start Property Wizard
                            </a>
                            <a href="/property/new" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-plus"></i> Add Property Manually
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function deleteProperty(propertyId) {
    if (confirm('Are you sure you want to delete this property? This action cannot be undone.')) {
        fetch(`/property/${propertyId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Failed to delete property');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete property');
        });
    }
}
</script>
{% endblock %}

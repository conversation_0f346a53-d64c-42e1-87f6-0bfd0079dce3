{% extends "base.html" %}

{% block title %}Edit Plant in Plan{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-green-800">Edit Plant in {{ plan.name }}</h1>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <form method="post" action="/season_plans/{{ plan.id }}/update_plant/{{ plant.id }}">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Plant</label>
                    <div class="text-md font-medium">{{ plant.name }}</div>
                    <input type="hidden" name="plant_id" value="{{ plant.id }}">
                </div>

                <div class="mb-4">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                    <input type="number" id="quantity" name="quantity" min="1" value="{{ plant.quantity }}" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                </div>

                {% if plan.growing_area %}
                <div class="grid grid-cols-2 gap-4 mb-4 col-span-2">
                    <div>
                        <label for="position_x" class="block text-sm font-medium text-gray-700 mb-1">Position X</label>
                        <input type="number" id="position_x" name="position_x" value="{% if plant.position_x %}{{ plant.position_x }}{% endif %}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                        <label for="position_y" class="block text-sm font-medium text-gray-700 mb-1">Position Y</label>
                        <input type="number" id="position_y" name="position_y" value="{% if plant.position_y %}{{ plant.position_y }}{% endif %}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>
                {% endif %}

                <div class="col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">{% if plant.notes %}{{ plant.notes }}{% endif %}</textarea>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <a href="/season_plans/{{ plan.id }}/view" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                    Cancel
                </a>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Update Plant
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
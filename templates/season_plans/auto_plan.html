{% extends "base.html" %}

{% block title %}Automatic Season Planning{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-green-800">Automatic Season Planning</h1>
            <a href="/season_plans" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                Back to Plans
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-2">AI-Powered Garden Planning</h2>
                <p class="text-gray-600">
                    Let our intelligent system create an optimized season plan for you. The system will automatically:
                </p>
                <ul class="list-disc list-inside text-gray-600 mt-2 space-y-1">
                    <li>Gather detailed plant information from botanical databases</li>
                    <li>Calculate optimal plant spacing and companion planting</li>
                    <li>Schedule succession plantings for continuous harvest</li>
                    <li>Optimize for your selected goals (yield, diversity, water efficiency)</li>
                    <li>Consider plant compatibility and growth requirements</li>
                </ul>
            </div>

            <form method="post" action="/season_plans/create_auto_plan" class="space-y-6">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="property_id" class="block text-sm font-medium text-gray-700 mb-1">Property *</label>
                        <select id="property_id" name="property_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">Select a property</option>
                            {% for property in properties %}
                            <option value="{{ property.id }}">{{ property.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="growing_area_id" class="block text-sm font-medium text-gray-700 mb-1">Growing Area (Optional)</label>
                        <select id="growing_area_id" name="growing_area_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">Use entire property</option>
                        </select>
                    </div>

                    <div>
                        <label for="season_id" class="block text-sm font-medium text-gray-700 mb-1">Season *</label>
                        <select id="season_id" name="season_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">Select a season</option>
                            {% for season in seasons %}
                            <option value="{{ season.id }}">{{ season.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date *</label>
                        <input type="date" id="start_date" name="start_date" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date *</label>
                        <input type="date" id="end_date" name="end_date" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>

                <!-- Plant Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Plants *</label>
                    <p class="text-sm text-gray-500 mb-3">Select the plants you want to include in your garden plan:</p>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-64 overflow-y-auto border border-gray-200 rounded-md p-4">
                        {% for plant in plants %}
                        <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                            <input type="checkbox" name="plant_{{ plant.id }}" value="{{ plant.id }}"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">{{ plant.name }}</span>
                        </label>
                        {% endfor %}
                    </div>
                    <input type="hidden" id="preferred_plants" name="preferred_plants" value="">
                </div>

                <!-- Optimization Goals -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Optimization Goals</label>
                    <p class="text-sm text-gray-500 mb-3">Choose your priorities for the automatic planning:</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="maximize_yield" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Maximize Yield</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="maximize_diversity" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Maximize Plant Diversity</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="minimize_water_usage" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Minimize Water Usage</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="maximize_companion_planting" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Maximize Companion Planting</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="enable_succession_planting" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Enable Succession Planting</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="prefer_perennials" value="true"
                                class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">Prefer Perennial Plants</span>
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <a href="/season_plans" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-6 rounded">
                        Cancel
                    </a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                        Generate Automatic Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const propertySelect = document.getElementById('property_id');
    const growingAreaSelect = document.getElementById('growing_area_id');
    const form = document.querySelector('form');
    
    // Handle property change to load growing areas
    propertySelect.addEventListener('change', async function() {
        const propertyId = this.value;
        if (!propertyId) {
            growingAreaSelect.innerHTML = '<option value="">Use entire property</option>';
            return;
        }
        
        try {
            const response = await fetch(`/api/properties/${propertyId}/growing_areas`);
            const areas = await response.json();
            
            let options = '<option value="">Use entire property</option>';
            areas.forEach(area => {
                const name = area.name || `Area ${area.id}`;
                options += `<option value="${area.id}">${name}</option>`;
            });
            
            growingAreaSelect.innerHTML = options;
        } catch (error) {
            console.error('Error fetching growing areas:', error);
        }
    });
    
    // Handle form submission to collect selected plants
    form.addEventListener('submit', function(e) {
        const selectedPlants = [];
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="plant_"]');
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedPlants.push(checkbox.value);
            }
        });
        
        if (selectedPlants.length === 0) {
            e.preventDefault();
            alert('Please select at least one plant for your garden plan.');
            return;
        }
        
        document.getElementById('preferred_plants').value = selectedPlants.join(',');
    });
    
    // Set default dates
    const today = new Date();
    const startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endDate = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
    
    document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
});
</script>
{% endblock %}

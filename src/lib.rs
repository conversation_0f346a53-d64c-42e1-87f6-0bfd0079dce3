pub mod models;
pub mod routes;
pub mod services;
pub mod schema;
pub mod utils;

use diesel::r2d2::{self, ConnectionManager};
use diesel::SqliteConnection;
use std::env;

pub type DbPool = r2d2::Pool<ConnectionManager<SqliteConnection>>;

// Database connection function
pub fn establish_connection_pool() -> DbPool {
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "garden_planner.db".to_string());

    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.")
}

// Re-export commonly used types for tests
pub use routes::*;

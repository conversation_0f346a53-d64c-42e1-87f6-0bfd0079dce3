use actix_session::Session;
use actix_web::{web, HttpResponse, Result, HttpRequest, ResponseError};
use serde::Deserialize;
use crate::models::plant::{NewPlant, Plant};
use crate::utils::templates::render_template;
use crate::DbPool;
use crate::services::plant_service::PlantService;
use crate::utils::error::AppError;

#[derive(Deserialize)]
pub struct PlantForm {
    pub name: String,
    pub description: Option<String>,
    pub latin_name: Option<String>,
    pub variety: Option<String>,
    pub note: Option<String>,
    pub nutrient_consumption: Option<String>,
    pub nutrient_deposit: Option<String>,
    pub lighting: Option<String>,
    pub temperature: Option<String>,
    pub light_amount: Option<String>,
    pub sowing_time: Option<String>,
    pub propagation_time: Option<String>,
    pub harvest_time: Option<String>,
    pub growth_duration: Option<String>,
    pub harvest_duration: Option<String>,
    pub field_id: Option<i32>,
}



pub async fn list_plants(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if let Some(username) = session.get::<String>("username")? {
        let all_plants = match PlantService::get_all_plants(&pool) {
            Ok(plants) => plants,
            Err(e) => {
                log::error!("Error loading plants: {}", e);
                return Ok(e.error_response());
            }
        };

        let mut ctx = tera::Context::new();
        ctx.insert("plants", &all_plants);
        ctx.insert("username", &username);

        Ok(render_template("plants/list.html", &ctx)?)
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn create_plant(
    session: Session,
    req: HttpRequest,
    form_data: web::Form<PlantForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        let new_plant = NewPlant {
            name: &form_data.name,
            description: form_data.description.as_deref(),
            latin_name: form_data.latin_name.as_deref(),
            variety: form_data.variety.as_deref(),
            note: form_data.note.as_deref(),
            nutrient_consumption: form_data.nutrient_consumption.as_deref(),
            nutrient_deposit: form_data.nutrient_deposit.as_deref(),
            lighting: form_data.lighting.as_deref(),
            temperature: form_data.temperature.as_deref(),
            light_amount: form_data.light_amount.as_deref(),
            sowing_time: form_data.sowing_time.as_deref(),
            propagation_time: form_data.propagation_time.as_deref(),
            harvest_time: form_data.harvest_time.as_deref(),
            growth_duration: form_data.growth_duration.as_deref(),
            harvest_duration: form_data.harvest_duration.as_deref(),
            field_id: form_data.field_id,
            herba_plant_id: None, // Will be set later by herba auto-gathering
        };

        let inserted_plant = match PlantService::create_plant(&pool, &new_plant) {
            Ok(plant) => plant,
            Err(e) => {
                log::error!("Error creating plant: {}", e);
                return Ok(e.error_response());
            }
        };

        // Check if the request is an AJAX request
        if req.headers().get("X-Requested-With").map_or(false, |h| h == "XMLHttpRequest") {
            // Return JSON response
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "plant": {
                    "id": inserted_plant.id,
                    "name": inserted_plant.name,
                    "latin_name": inserted_plant.latin_name,
                    "field_id": inserted_plant.field_id,
                    // Include other fields as necessary
                }
            })))
        } else {
            // Redirect for normal form submissions
            Ok(HttpResponse::Found()
                .append_header(("Location", "/plants/list"))
                .finish())
        }
    } else {
        Ok(HttpResponse::Unauthorized().finish())
    }
}

pub async fn edit_plant_form(
    session: Session,
    plant_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let plant = match PlantService::get_plant_by_id(&pool, *plant_id) {
            Ok(Some(plant)) => plant,
            Ok(None) => {
                let error = AppError::NotFoundError(format!("Plant with ID {} not found", *plant_id));
                return Ok(error.error_response());
            },
            Err(e) => {
                log::error!("Error loading plant: {}", e);
                return Ok(e.error_response());
            }
        };

        let mut ctx = tera::Context::new();
        ctx.insert("plant", &plant);
        Ok(render_template("plants/edit.html", &ctx)?)
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}


pub async fn update_plant(
    session: Session,
    plant_id: web::Path<i32>,
    form: web::Form<PlantForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let updated_plant = Plant {
            id: *plant_id,
            name: form.name.clone(),
            description: form.description.clone(),
            latin_name: form.latin_name.clone(),
            variety: form.variety.clone(),
            note: form.note.clone(),
            nutrient_consumption: form.nutrient_consumption.clone(),
            nutrient_deposit: form.nutrient_deposit.clone(),
            lighting: form.lighting.clone(),
            temperature: form.temperature.clone(),
            light_amount: form.light_amount.clone(),
            sowing_time: form.sowing_time.clone(),
            propagation_time: form.propagation_time.clone(),
            harvest_time: form.harvest_time.clone(),
            growth_duration: form.growth_duration.clone(),
            harvest_duration: form.harvest_duration.clone(),
            field_id: form.field_id,
            herba_plant_id: None, // Keep existing herba_plant_id or update via service
        };

        if let Err(e) = PlantService::update_plant(&pool, *plant_id, &updated_plant) {
            log::error!("Error updating plant: {}", e);
            return Ok(e.error_response());
        }

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plants/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn new_plant_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        render_template("plants/new.html", &tera::Context::new())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}


pub async fn delete_plant(
    session: Session,
    plant_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        if let Err(e) = PlantService::delete_plant(&pool, *plant_id) {
            log::error!("Error deleting plant: {}", e);
            return Ok(e.error_response());
        }

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plants/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}


pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/plants")
            .route("/list", web::get().to(list_plants))
            .route("/new", web::get().to(new_plant_form))
            .route("/create", web::post().to(create_plant))
            .route("/{id}/edit", web::get().to(edit_plant_form))
            .route("/{id}/update", web::post().to(update_plant))
            .route("/{id}/delete", web::post().to(delete_plant)),
    );
}

use actix_session::Session;
use actix_web::{web, HttpResponse, Result};

use crate::utils::templates::{render_template, render_template_with_context};
use crate::utils::auth::is_authenticated;

pub async fn admin_dashboard(
    session: Session,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if is_admin {
        let mut ctx = tera::Context::new();
        render_template_with_context("admin/dashboard.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."))
    }
}

// Add a simple admin index route
pub async fn admin_index(session: Session) -> Result<HttpResponse, actix_web::Error> {
    admin_dashboard(session).await
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/admin")
            .route("", web::get().to(admin_index))
            .route("/", web::get().to(admin_index))
            .route("/dashboard", web::get().to(admin_dashboard)),
    );
}

use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;
use chrono::NaiveDate;

use crate::models::season::{NewSeason, Season};
use crate::utils::templates::{render_template, render_template_with_context};
use crate::DbPool;
use crate::schema::seasons;

#[derive(Deserialize)]
pub struct SeasonForm {
    pub name: String,
    pub start_date: String,
    pub end_date: String,
}

pub async fn list_seasons(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let all_seasons = seasons::table
        .order(seasons::start_date.asc())
        .load::<Season>(&mut conn)
        .expect("Error loading seasons");

    let mut ctx = tera::Context::new();
    ctx.insert("seasons", &all_seasons);

    Ok(render_template_with_context("seasons/list.html", &mut ctx, &session)?)
}

pub async fn new_season_form(session: Session) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        Ok(render_template("seasons/new.html", &tera::Context::new())?)
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn create_season(
    session: Session,
    form: web::Form<SeasonForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if session.get::<String>("username")?.is_some() {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        let new_season = NewSeason {
            name: &form.name,
            start_date: NaiveDate::parse_from_str(&form.start_date, "%Y-%m-%d").unwrap(),
            end_date: NaiveDate::parse_from_str(&form.end_date, "%Y-%m-%d").unwrap(),
        };

        diesel::insert_into(seasons::table)
            .values(&new_season)
            .execute(&mut conn)
            .expect("Error inserting new season");

        Ok(HttpResponse::Found()
            .append_header(("Location", "/seasons/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/seasons")
            .route("/list", web::get().to(list_seasons))
            .route("/new", web::get().to(new_season_form))
            .route("/create", web::post().to(create_season)),
    );
}

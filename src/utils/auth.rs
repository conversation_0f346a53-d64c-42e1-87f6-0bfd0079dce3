use actix_session::Session;
use actix_web::Result;
use bcrypt::{hash, verify, DEFAULT_COST};

/// Check if a user is authenticated
pub fn is_authenticated(session: &Session) -> bool {
    session.get::<i32>("user_id").unwrap_or(None).is_some()
}

/// Hash a password using bcrypt
pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptError> {
    hash(password, DEFAULT_COST)
}

/// Verify a password against a hash
pub fn verify_password(password: &str, hash: &str) -> Result<bool, bcrypt::BcryptError> {
    verify(password, hash)
}

pub fn _is_authorized(session: &Session, required_role: &str) -> Result<bool> {
    if let Some(role) = session.get::<String>("role")? {
        match required_role {
            "superadmin" => Ok(role == "superadmin"),
            "admin" => Ok(role == "superadmin" || role == "admin"),
            "moderator" => Ok(role == "superadmin" || role == "admin" || role == "moderator"),
            "user" => Ok(true),
            _ => Ok(false),
        }
    } else {
        Ok(false)
    }
}
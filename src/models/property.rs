use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;

#[derive(Queryable, Selectable, Serialize, Deserialize)]
#[diesel(table_name = crate::schema::properties)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct Property {
    pub id: Option<i32>,
    pub name: String,
    pub inside_area: Option<f32>,
    pub outside_area: Option<f32>,
    pub floors: i32,
    pub household_id: i32,
    pub owner_id: i32,
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
}

#[derive(Insertable)]
#[diesel(table_name = crate::schema::properties)]
pub struct NewProperty<'a> {
    pub name: &'a str,
    pub inside_area: Option<f32>,
    pub outside_area: Option<f32>,
    pub floors: i32,
    pub household_id: i32,
    pub owner_id: i32,
}

impl Property {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<Property>> {
        use crate::schema::properties::dsl::*;
        properties.load::<Property>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, property_id: i32) -> QueryResult<Option<Property>> {
        use crate::schema::properties::dsl::*;
        properties
            .filter(id.eq(Some(property_id)))
            .first(conn)
            .optional()
    }

    pub fn find_by_user_id(conn: &mut SqliteConnection, user_id: i32) -> QueryResult<Vec<Property>> {
        use crate::schema::properties::dsl::*;
        properties.filter(owner_id.eq(user_id)).load::<Property>(conn)
    }
}

// Re-export the property shape and growing area models
pub use crate::models::property_shape::{PropertyShape, NewPropertyShape};
pub use crate::models::growing_area_shape::{GrowingAreaShape as GrowingArea, NewGrowingAreaShape};

use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;

#[derive(Queryable, Selectable, Serialize, Deserialize)]
#[diesel(table_name = crate::schema::growing_area_shapes)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct GrowingAreaShape {
    pub id: i32,
    pub property_id: i32,
    pub shape_data: String,
    pub shape_type: String,
    pub floor_no: i32,
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
    pub area: Option<f32>,
}

#[derive(Insertable)]
#[diesel(table_name = crate::schema::growing_area_shapes)]
pub struct NewGrowingAreaShape<'a> {
    pub property_id: i32,
    pub shape_data: &'a str,
    pub shape_type: &'a str,
    pub floor_no: i32,
    pub area: Option<f32>,
}

impl GrowingAreaShape {
    pub fn find_by_id(conn: &mut SqliteConnection, shape_id: i32) -> QueryResult<Option<GrowingAreaShape>> {
        use crate::schema::growing_area_shapes::dsl::*;
        growing_area_shapes.find(shape_id).first(conn).optional()
    }

    pub fn find_by_property_id(conn: &mut SqliteConnection, prop_id: i32) -> QueryResult<Vec<GrowingAreaShape>> {
        use crate::schema::growing_area_shapes::dsl::*;
        growing_area_shapes.filter(property_id.eq(prop_id)).load::<GrowingAreaShape>(conn)
    }
}

use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::NaiveDate;

use crate::schema::seasons;

#[derive(Debug, Clone, Queryable, Identifiable, Serialize, Deserialize, AsChangeset)]
#[diesel(table_name = seasons)]
pub struct Season {
    pub id: i32,
    pub name: String,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = seasons)]
pub struct NewSeason<'a> {
    pub name: &'a str,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

impl Season {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<Season>> {
        seasons::table.load::<Season>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, season_id: i32) -> QueryResult<Option<Season>> {
        seasons::table
            .filter(seasons::id.eq(season_id))
            .first(conn)
            .optional()
    }
}

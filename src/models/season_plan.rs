use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::{NaiveDate, NaiveDateTime};

use crate::schema::season_plans;

#[derive(Debug, Queryable, Selectable, Serialize, Deserialize, Identifiable)]
#[diesel(table_name = season_plans)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct SeasonPlan {
    pub id: Option<i32>,
    pub name: String,
    pub season_id: i32,
    pub property_id: i32,
    pub growing_area_id: Option<i32>,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub description: Option<String>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = season_plans)]
pub struct NewSeasonPlan<'a> {
    pub name: &'a str,
    pub season_id: i32,
    pub property_id: i32,
    pub growing_area_id: Option<i32>,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub description: Option<&'a str>,
}

impl SeasonPlan {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<SeasonPlan>> {
        season_plans::table
            .order(season_plans::start_date.desc())
            .load::<SeasonPlan>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, id: i32) -> QueryResult<Option<SeasonPlan>> {
        season_plans::table
            .filter(season_plans::id.eq(Some(id)))
            .first(conn)
            .optional()
    }

    pub fn find_by_property_id(conn: &mut SqliteConnection, property_id: i32) -> QueryResult<Vec<SeasonPlan>> {
        season_plans::table
            .filter(season_plans::property_id.eq(property_id))
            .order(season_plans::start_date.desc())
            .load::<SeasonPlan>(conn)
    }

    pub fn create(conn: &mut SqliteConnection, new_plan: &NewSeasonPlan) -> QueryResult<i32> {
        use crate::schema::season_plans::dsl::*;

        diesel::insert_into(season_plans)
            .values(new_plan)
            .execute(conn)?;

        // Get the last inserted row ID
        let last_id: i64 = diesel::select(diesel::dsl::sql::<diesel::sql_types::BigInt>("last_insert_rowid()"))
            .get_result(conn)?;

        Ok(last_id as i32)
    }

    pub fn update(conn: &mut SqliteConnection, plan_id: i32, updated_plan: &NewSeasonPlan) -> QueryResult<usize> {
        use crate::schema::season_plans::dsl::*;

        diesel::update(season_plans.filter(id.eq(Some(plan_id))))
            .set((
                name.eq(updated_plan.name),
                season_id.eq(updated_plan.season_id),
                property_id.eq(updated_plan.property_id),
                growing_area_id.eq(updated_plan.growing_area_id),
                start_date.eq(updated_plan.start_date),
                end_date.eq(updated_plan.end_date),
                description.eq(updated_plan.description),
            ))
            .execute(conn)
    }

    pub fn delete(conn: &mut SqliteConnection, plan_id: i32) -> QueryResult<usize> {
        use crate::schema::season_plans::dsl::*;

        diesel::delete(season_plans.filter(id.eq(Some(plan_id))))
            .execute(conn)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeasonPlanWithDetails {
    pub id: i32,
    pub season_id: i32,
    pub season_name: String,
    pub name: String,
    pub description: Option<String>,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub property_id: i32,
    pub property_name: String,
    pub growing_area_id: Option<i32>,
    pub plants: Vec<SeasonPlanPlant>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeasonPlanPlant {
    pub id: i32,
    pub plan_id: i32,
    pub plant_id: i32,
    pub plant_name: String,
    pub quantity: i32,
    pub position_x: Option<i32>,
    pub position_y: Option<i32>,
    pub notes: Option<String>,
}

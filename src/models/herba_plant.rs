use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;

/// Herba database plant record - represents meta information about plant species
/// This allows linking multiple individual plants to the same botanical species
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = crate::schema::herba_plants)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct HerbaPlant {
    pub id: i32,
    pub latin_name: String,
    pub common_name: String,
    pub family: Option<String>,
    pub genus: Option<String>,
    pub species: Option<String>,
    pub variety: Option<String>,
    pub description: Option<String>,
    pub growth_habit: Option<String>, // annual, perennial, biennial
    pub mature_height: Option<String>,
    pub mature_width: Option<String>,
    pub sun_requirements: Option<String>, // full sun, partial shade, full shade
    pub water_requirements: Option<String>, // low, medium, high
    pub soil_type: Option<String>,
    pub soil_ph: Option<String>,
    pub hardiness_zone: Option<String>,
    pub bloom_time: Option<String>,
    pub bloom_color: Option<String>,
    pub foliage_color: Option<String>,
    pub fruit_time: Option<String>,
    pub companion_plants: Option<String>, // JSON array of compatible plant IDs
    pub antagonist_plants: Option<String>, // JSON array of incompatible plant IDs
    pub pest_resistance: Option<String>,
    pub disease_resistance: Option<String>,
    pub edible_parts: Option<String>, // leaves, fruits, roots, flowers, etc.
    pub medicinal_uses: Option<String>,
    pub culinary_uses: Option<String>,
    pub propagation_methods: Option<String>, // seed, cutting, division, etc.
    pub seed_germination_time: Option<i32>, // days
    pub seed_viability: Option<i32>, // years
    pub days_to_maturity: Option<i32>,
    pub harvest_method: Option<String>,
    pub storage_method: Option<String>,
    pub nutritional_info: Option<String>, // JSON with vitamins, minerals, etc.
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = crate::schema::herba_plants)]
pub struct NewHerbaPlant<'a> {
    pub latin_name: &'a str,
    pub common_name: &'a str,
    pub family: Option<&'a str>,
    pub genus: Option<&'a str>,
    pub species: Option<&'a str>,
    pub variety: Option<&'a str>,
    pub description: Option<&'a str>,
    pub growth_habit: Option<&'a str>,
    pub mature_height: Option<&'a str>,
    pub mature_width: Option<&'a str>,
    pub sun_requirements: Option<&'a str>,
    pub water_requirements: Option<&'a str>,
    pub soil_type: Option<&'a str>,
    pub soil_ph: Option<&'a str>,
    pub hardiness_zone: Option<&'a str>,
    pub bloom_time: Option<&'a str>,
    pub bloom_color: Option<&'a str>,
    pub foliage_color: Option<&'a str>,
    pub fruit_time: Option<&'a str>,
    pub companion_plants: Option<&'a str>,
    pub antagonist_plants: Option<&'a str>,
    pub pest_resistance: Option<&'a str>,
    pub disease_resistance: Option<&'a str>,
    pub edible_parts: Option<&'a str>,
    pub medicinal_uses: Option<&'a str>,
    pub culinary_uses: Option<&'a str>,
    pub propagation_methods: Option<&'a str>,
    pub seed_germination_time: Option<i32>,
    pub seed_viability: Option<i32>,
    pub days_to_maturity: Option<i32>,
    pub harvest_method: Option<&'a str>,
    pub storage_method: Option<&'a str>,
    pub nutritional_info: Option<&'a str>,
}

impl HerbaPlant {
    /// Find herba plant by ID
    pub fn find_by_id(conn: &mut SqliteConnection, herba_id: i32) -> QueryResult<Option<HerbaPlant>> {
        use crate::schema::herba_plants::dsl::*;
        herba_plants.find(herba_id).first(conn).optional()
    }

    /// Find herba plant by latin name
    pub fn find_by_latin_name(conn: &mut SqliteConnection, name: &str) -> QueryResult<Option<HerbaPlant>> {
        use crate::schema::herba_plants::dsl::*;
        herba_plants.filter(latin_name.eq(name)).first(conn).optional()
    }

    /// Find herba plants by common name (partial match)
    pub fn find_by_common_name(conn: &mut SqliteConnection, name: &str) -> QueryResult<Vec<HerbaPlant>> {
        use crate::schema::herba_plants::dsl::*;
        herba_plants
            .filter(common_name.like(format!("%{}%", name)))
            .load::<HerbaPlant>(conn)
    }

    /// Get all herba plants
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<HerbaPlant>> {
        use crate::schema::herba_plants::dsl::*;
        herba_plants.order(common_name.asc()).load::<HerbaPlant>(conn)
    }

    /// Create a new herba plant
    pub fn create(conn: &mut SqliteConnection, new_herba_plant: &NewHerbaPlant) -> QueryResult<HerbaPlant> {
        use crate::schema::herba_plants::dsl::*;

        diesel::insert_into(herba_plants)
            .values(new_herba_plant)
            .execute(conn)?;

        // Get the last inserted record
        herba_plants.order(id.desc()).first(conn)
    }

    /// Update an existing herba plant
    pub fn update(conn: &mut SqliteConnection, herba_id: i32, updated_herba_plant: &HerbaPlant) -> QueryResult<HerbaPlant> {
        use crate::schema::herba_plants::dsl::*;

        diesel::update(herba_plants.find(herba_id))
            .set((
                latin_name.eq(&updated_herba_plant.latin_name),
                common_name.eq(&updated_herba_plant.common_name),
                family.eq(&updated_herba_plant.family),
                genus.eq(&updated_herba_plant.genus),
                species.eq(&updated_herba_plant.species),
                variety.eq(&updated_herba_plant.variety),
                description.eq(&updated_herba_plant.description),
                growth_habit.eq(&updated_herba_plant.growth_habit),
                mature_height.eq(&updated_herba_plant.mature_height),
                mature_width.eq(&updated_herba_plant.mature_width),
                sun_requirements.eq(&updated_herba_plant.sun_requirements),
                water_requirements.eq(&updated_herba_plant.water_requirements),
                soil_type.eq(&updated_herba_plant.soil_type),
                soil_ph.eq(&updated_herba_plant.soil_ph),
                hardiness_zone.eq(&updated_herba_plant.hardiness_zone),
                bloom_time.eq(&updated_herba_plant.bloom_time),
                bloom_color.eq(&updated_herba_plant.bloom_color),
                foliage_color.eq(&updated_herba_plant.foliage_color),
                fruit_time.eq(&updated_herba_plant.fruit_time),
                companion_plants.eq(&updated_herba_plant.companion_plants),
                antagonist_plants.eq(&updated_herba_plant.antagonist_plants),
                pest_resistance.eq(&updated_herba_plant.pest_resistance),
                disease_resistance.eq(&updated_herba_plant.disease_resistance),
                edible_parts.eq(&updated_herba_plant.edible_parts),
                medicinal_uses.eq(&updated_herba_plant.medicinal_uses),
                culinary_uses.eq(&updated_herba_plant.culinary_uses),
                propagation_methods.eq(&updated_herba_plant.propagation_methods),
                seed_germination_time.eq(&updated_herba_plant.seed_germination_time),
                seed_viability.eq(&updated_herba_plant.seed_viability),
                days_to_maturity.eq(&updated_herba_plant.days_to_maturity),
                harvest_method.eq(&updated_herba_plant.harvest_method),
                storage_method.eq(&updated_herba_plant.storage_method),
                nutritional_info.eq(&updated_herba_plant.nutritional_info),
                updated_at.eq(chrono::Utc::now().naive_utc()),
            ))
            .execute(conn)?;

        // Return the updated record
        herba_plants.find(herba_id).first(conn)
    }

    /// Delete a herba plant
    pub fn delete(conn: &mut SqliteConnection, herba_id: i32) -> QueryResult<usize> {
        use crate::schema::herba_plants::dsl::*;
        diesel::delete(herba_plants.find(herba_id)).execute(conn)
    }

    /// Get companion plants for this herba plant
    pub fn get_companion_plants(&self, conn: &mut SqliteConnection) -> QueryResult<Vec<HerbaPlant>> {
        if let Some(companion_ids) = &self.companion_plants {
            if let Ok(ids) = serde_json::from_str::<Vec<i32>>(companion_ids) {
                use crate::schema::herba_plants::dsl::*;
                return herba_plants.filter(id.eq_any(ids)).load::<HerbaPlant>(conn);
            }
        }
        Ok(vec![])
    }

    /// Get antagonist plants for this herba plant
    pub fn get_antagonist_plants(&self, conn: &mut SqliteConnection) -> QueryResult<Vec<HerbaPlant>> {
        if let Some(antagonist_ids) = &self.antagonist_plants {
            if let Ok(ids) = serde_json::from_str::<Vec<i32>>(antagonist_ids) {
                use crate::schema::herba_plants::dsl::*;
                return herba_plants.filter(id.eq_any(ids)).load::<HerbaPlant>(conn);
            }
        }
        Ok(vec![])
    }

    /// Check if this plant is compatible with another plant
    pub fn is_compatible_with(&self, other_herba_id: i32) -> bool {
        // Check if the other plant is in our antagonist list
        if let Some(antagonist_ids) = &self.antagonist_plants {
            if let Ok(ids) = serde_json::from_str::<Vec<i32>>(antagonist_ids) {
                if ids.contains(&other_herba_id) {
                    return false;
                }
            }
        }

        // Check if the other plant is in our companion list (optional positive check)
        if let Some(companion_ids) = &self.companion_plants {
            if let Ok(ids) = serde_json::from_str::<Vec<i32>>(companion_ids) {
                return ids.contains(&other_herba_id);
            }
        }

        // If not explicitly incompatible, assume neutral compatibility
        true
    }

    /// Get watering schedule recommendation based on water requirements
    pub fn get_watering_schedule(&self) -> Option<i32> {
        match self.water_requirements.as_deref() {
            Some("low") => Some(7), // Water every 7 days
            Some("medium") => Some(3), // Water every 3 days
            Some("high") => Some(1), // Water daily
            _ => Some(5), // Default to every 5 days
        }
    }

    /// Get fertilizing schedule recommendation
    pub fn get_fertilizing_schedule(&self) -> Option<i32> {
        match self.growth_habit.as_deref() {
            Some("annual") => Some(14), // Fertilize every 2 weeks
            Some("perennial") => Some(30), // Fertilize monthly
            Some("biennial") => Some(21), // Fertilize every 3 weeks
            _ => Some(21), // Default to every 3 weeks
        }
    }
}

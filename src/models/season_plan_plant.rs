use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;

use crate::schema::season_plan_plants;

#[derive(Debug, Queryable, Selectable, Serialize, Deserialize, Identifiable)]
#[diesel(table_name = season_plan_plants)]
#[diesel(check_for_backend(diesel::sqlite::Sqlite))]
pub struct SeasonPlanPlant {
    pub id: Option<i32>,
    pub plan_id: i32,
    pub plant_id: i32,
    pub quantity: i32,
    pub position_x: Option<i32>,
    pub position_y: Option<i32>,
    pub notes: Option<String>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = season_plan_plants)]
pub struct NewSeasonPlanPlant<'a> {
    pub plan_id: i32,
    pub plant_id: i32,
    pub quantity: i32,
    pub position_x: Option<i32>,
    pub position_y: Option<i32>,
    pub notes: Option<&'a str>,
}

impl SeasonPlanPlant {
    pub fn find_by_id(conn: &mut SqliteConnection, id: i32) -> QueryResult<Option<SeasonPlanPlant>> {
        season_plan_plants::table
            .filter(season_plan_plants::id.eq(Some(id)))
            .first(conn)
            .optional()
    }

    pub fn find_by_plan_id(conn: &mut SqliteConnection, plan_id: i32) -> QueryResult<Vec<SeasonPlanPlant>> {
        season_plan_plants::table
            .filter(season_plan_plants::plan_id.eq(plan_id))
            .load::<SeasonPlanPlant>(conn)
    }

    pub fn count_by_plan_id(conn: &mut SqliteConnection, plan_id: i32) -> QueryResult<i64> {
        season_plan_plants::table
            .filter(season_plan_plants::plan_id.eq(plan_id))
            .count()
            .get_result(conn)
    }

    pub fn create(conn: &mut SqliteConnection, new_plant: &NewSeasonPlanPlant) -> QueryResult<i32> {
        use crate::schema::season_plan_plants::dsl::*;

        diesel::insert_into(season_plan_plants)
            .values(new_plant)
            .execute(conn)?;

        // Get the last inserted row ID
        let last_id: i64 = diesel::select(diesel::dsl::sql::<diesel::sql_types::BigInt>("last_insert_rowid()"))
            .get_result(conn)?;

        Ok(last_id as i32)
    }

    pub fn update(conn: &mut SqliteConnection, plant_id_val: i32, updated_plant: &NewSeasonPlanPlant) -> QueryResult<usize> {
        use crate::schema::season_plan_plants::dsl::*;

        diesel::update(season_plan_plants.filter(id.eq(Some(plant_id_val))))
            .set((
                quantity.eq(updated_plant.quantity),
                position_x.eq(updated_plant.position_x),
                position_y.eq(updated_plant.position_y),
                notes.eq(updated_plant.notes),
            ))
            .execute(conn)
    }

    pub fn delete(conn: &mut SqliteConnection, plant_id_val: i32) -> QueryResult<usize> {
        use crate::schema::season_plan_plants::dsl::*;

        diesel::delete(season_plan_plants.filter(id.eq(Some(plant_id_val))))
            .execute(conn)
    }

    pub fn delete_by_plan_id(conn: &mut SqliteConnection, plan_id_val: i32) -> QueryResult<usize> {
        use crate::schema::season_plan_plants::dsl::*;

        diesel::delete(season_plan_plants.filter(plan_id.eq(plan_id_val)))
            .execute(conn)
    }

    /// Find all season plan plants for a specific user
    pub fn find_all_for_user(conn: &mut SqliteConnection, user_id: i32) -> QueryResult<Vec<SeasonPlanPlant>> {
        use crate::schema::season_plan_plants;
        use crate::schema::season_plans;
        use crate::schema::properties;
        use crate::schema::user_households;

        // For now, return all season plan plants - we'll implement proper user filtering later
        season_plan_plants::table.load::<SeasonPlanPlant>(conn)
    }
}
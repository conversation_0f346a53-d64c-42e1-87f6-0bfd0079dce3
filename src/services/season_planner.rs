use crate::models::{Plant, HerbaPlant, SeasonPlan, SeasonPlanPlant, GrowingArea, Property, Season};
use crate::services::herba_gatherer::{HerbaGatherer, PlantIdentifier};
use diesel::SqliteConnection;
use chrono::{NaiveDate, Datelike};
use serde_json::json;
use std::collections::HashMap;

/// Automatic season planner that optimizes crop yields and plant placement
pub struct SeasonPlanner {
    herba_gatherer: HerbaGatherer,
}

#[derive(Debug, Clone)]
pub struct PlantingRecommendation {
    pub plant_id: i32,
    pub herba_plant_id: Option<i32>,
    pub quantity: i32,
    pub position_x: Option<i32>,
    pub position_y: Option<i32>,
    pub planting_date: NaiveDate,
    pub harvest_date: NaiveDate,
    pub companion_plants: Vec<i32>,
    pub spacing_requirements: f32,
    pub notes: String,
}

#[derive(Debug, Clone)]
pub struct SeasonPlanOptimization {
    pub total_yield_score: f32,
    pub space_efficiency: f32,
    pub companion_planting_score: f32,
    pub succession_planting_score: f32,
    pub water_efficiency: f32,
    pub nutrient_balance: f32,
}

#[derive(Debug, Clone)]
pub struct AutoPlanRequest {
    pub property_id: i32,
    pub growing_area_id: Option<i32>,
    pub season_id: i32,
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub preferred_plants: Vec<i32>, // Plant IDs user wants to grow
    pub optimization_goals: OptimizationGoals,
}

#[derive(Debug, Clone)]
pub struct OptimizationGoals {
    pub maximize_yield: bool,
    pub maximize_diversity: bool,
    pub minimize_water_usage: bool,
    pub maximize_companion_planting: bool,
    pub enable_succession_planting: bool,
    pub prefer_perennials: bool,
}

impl SeasonPlanner {
    pub fn new() -> Self {
        Self {
            herba_gatherer: HerbaGatherer::new(),
        }
    }

    /// Main entry point for automatic season planning
    pub async fn create_automatic_plan(
        &self,
        conn: &mut SqliteConnection,
        request: AutoPlanRequest,
    ) -> Result<Vec<PlantingRecommendation>, Box<dyn std::error::Error>> {
        // 1. Analyze growing space
        let growing_area = self.analyze_growing_space(conn, &request)?;

        // 2. Gather plant information and ensure herba data exists
        let plants_with_herba = self.ensure_herba_data_for_plants(conn, &request.preferred_plants).await?;

        // 3. Calculate optimal plant placement
        let recommendations = self.calculate_optimal_placement(
            conn,
            &request,
            &growing_area,
            &plants_with_herba,
        )?;

        // 4. Apply succession planting if enabled
        let final_recommendations = if request.optimization_goals.enable_succession_planting {
            self.apply_succession_planting(conn, recommendations, &request)?
        } else {
            recommendations
        };

        Ok(final_recommendations)
    }

    /// Analyze the growing space to understand constraints
    fn analyze_growing_space(
        &self,
        conn: &mut SqliteConnection,
        request: &AutoPlanRequest,
    ) -> Result<GrowingSpaceAnalysis, Box<dyn std::error::Error>> {
        let property = Property::find_by_id(conn, request.property_id)?
            .ok_or("Property not found")?;

        let growing_area = if let Some(area_id) = request.growing_area_id {
            Some(GrowingArea::find_by_id(conn, area_id)?.ok_or("Growing area not found")?)
        } else {
            None
        };

        let total_area = growing_area
            .as_ref()
            .and_then(|ga| ga.area)
            .unwrap_or(property.outside_area.unwrap_or(100.0));

        Ok(GrowingSpaceAnalysis {
            total_area,
            available_area: total_area * 0.8, // 80% usable space
            is_indoor: growing_area.is_some(),
            soil_quality: SoilQuality::Medium, // Default assumption
            sun_exposure: SunExposure::FullSun, // Default assumption
            water_access: WaterAccess::Good, // Default assumption
        })
    }

    /// Ensure all plants have herba data, gathering it automatically if needed
    async fn ensure_herba_data_for_plants(
        &self,
        conn: &mut SqliteConnection,
        plant_ids: &[i32],
    ) -> Result<Vec<PlantWithHerba>, Box<dyn std::error::Error>> {
        let mut plants_with_herba = Vec::new();

        for &plant_id in plant_ids {
            if let Some(plant) = Plant::find_by_id(conn, plant_id)? {
                let herba_plant = if let Some(herba_id) = plant.herba_plant_id {
                    // Herba data already exists
                    HerbaPlant::find_by_id(conn, herba_id)?
                } else {
                    // Need to gather herba data
                    let identifier = PlantIdentifier {
                        latin_name: plant.latin_name.clone(),
                        common_name: Some(plant.name.clone()),
                        partial_name: Some(plant.name.clone()),
                        family: None,
                        genus: None,
                    };

                    match self.herba_gatherer.gather_plant_info(identifier).await {
                        Ok(gathered_data) => {
                            let herba_plant = self.herba_gatherer.save_to_database(gathered_data, conn).await?;

                            // Update plant with herba_plant_id
                            self.update_plant_herba_id(conn, plant_id, herba_plant.id)?;

                            Some(herba_plant)
                        }
                        Err(e) => {
                            log::warn!("Failed to gather herba data for plant {}: {}", plant.name, e);
                            None
                        }
                    }
                };

                plants_with_herba.push(PlantWithHerba {
                    plant,
                    herba_plant,
                });
            }
        }

        Ok(plants_with_herba)
    }

    /// Update plant with herba_plant_id
    fn update_plant_herba_id(
        &self,
        conn: &mut SqliteConnection,
        plant_id: i32,
        herba_id: i32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::schema::plants::dsl::*;
        use diesel::prelude::*;

        diesel::update(plants.find(plant_id))
            .set(herba_plant_id.eq(Some(herba_id)))
            .execute(conn)?;

        Ok(())
    }

    /// Calculate optimal plant placement using advanced algorithms
    fn calculate_optimal_placement(
        &self,
        conn: &mut SqliteConnection,
        request: &AutoPlanRequest,
        growing_space: &GrowingSpaceAnalysis,
        plants_with_herba: &[PlantWithHerba],
    ) -> Result<Vec<PlantingRecommendation>, Box<dyn std::error::Error>> {
        let mut recommendations = Vec::new();
        let mut used_area = 0.0;
        let mut placement_grid = PlacementGrid::new(100, 100); // 100x100 grid

        for plant_with_herba in plants_with_herba {
            if used_area >= growing_space.available_area {
                break;
            }

            let plant = &plant_with_herba.plant;
            let herba = &plant_with_herba.herba_plant;

            // Calculate space requirements
            let space_needed = self.calculate_space_requirements(herba);
            if used_area + space_needed > growing_space.available_area {
                continue;
            }

            // Find optimal position considering companion planting
            let position = self.find_optimal_position(
                &placement_grid,
                space_needed,
                plant,
                herba,
                &recommendations,
            );

            // Calculate planting and harvest dates
            let (planting_date, harvest_date) = self.calculate_planting_dates(
                &request.start_date,
                &request.end_date,
                herba,
            );

            // Determine quantity based on space and yield goals
            let quantity = self.calculate_optimal_quantity(
                space_needed,
                growing_space.available_area - used_area,
                &request.optimization_goals,
                herba,
            );

            let recommendation = PlantingRecommendation {
                plant_id: plant.id,
                herba_plant_id: herba.as_ref().map(|h| h.id),
                quantity,
                position_x: position.map(|p| p.0),
                position_y: position.map(|p| p.1),
                planting_date,
                harvest_date,
                companion_plants: self.find_companion_plants(&recommendations, herba),
                spacing_requirements: space_needed,
                notes: self.generate_planting_notes(plant, herba, &request.optimization_goals),
            };

            // Update placement grid
            if let Some((x, y)) = position {
                placement_grid.mark_used(x, y, space_needed as i32);
            }

            used_area += space_needed * quantity as f32;
            recommendations.push(recommendation);
        }

        Ok(recommendations)
    }

    /// Apply succession planting to maximize continuous harvest
    fn apply_succession_planting(
        &self,
        conn: &mut SqliteConnection,
        mut recommendations: Vec<PlantingRecommendation>,
        request: &AutoPlanRequest,
    ) -> Result<Vec<PlantingRecommendation>, Box<dyn std::error::Error>> {
        let mut succession_recommendations = Vec::new();

        for recommendation in recommendations.iter() {
            succession_recommendations.push(recommendation.clone());

            // Check if plant is suitable for succession planting
            if self.is_suitable_for_succession(recommendation) {
                let succession_interval = self.calculate_succession_interval(recommendation);
                let mut current_date = recommendation.planting_date;

                while current_date + chrono::Duration::days(succession_interval as i64) <= request.end_date {
                    current_date = current_date + chrono::Duration::days(succession_interval as i64);

                    let mut succession_rec = recommendation.clone();
                    succession_rec.planting_date = current_date;
                    succession_rec.harvest_date = current_date + chrono::Duration::days(90); // Default 90 days
                    succession_rec.notes = format!("{} (Succession planting)", succession_rec.notes);

                    succession_recommendations.push(succession_rec);
                }
            }
        }

        Ok(succession_recommendations)
    }

    // Helper methods
    fn calculate_space_requirements(&self, herba: &Option<HerbaPlant>) -> f32 {
        if let Some(h) = herba {
            // Parse mature width/height to calculate space
            // This is a simplified calculation
            1.0 // Default 1 square meter per plant
        } else {
            0.5 // Default for unknown plants
        }
    }

    fn find_optimal_position(
        &self,
        grid: &PlacementGrid,
        space_needed: f32,
        plant: &Plant,
        herba: &Option<HerbaPlant>,
        existing_recommendations: &[PlantingRecommendation],
    ) -> Option<(i32, i32)> {
        // Simplified position finding - in reality this would be much more sophisticated
        Some((10, 10))
    }

    fn calculate_planting_dates(
        &self,
        start_date: &NaiveDate,
        end_date: &NaiveDate,
        herba: &Option<HerbaPlant>,
    ) -> (NaiveDate, NaiveDate) {
        let planting_date = *start_date;
        let harvest_date = if let Some(h) = herba {
            planting_date + chrono::Duration::days(h.days_to_maturity.unwrap_or(90) as i64)
        } else {
            planting_date + chrono::Duration::days(90)
        };
        (planting_date, harvest_date)
    }

    fn calculate_optimal_quantity(
        &self,
        space_per_plant: f32,
        available_space: f32,
        goals: &OptimizationGoals,
        herba: &Option<HerbaPlant>,
    ) -> i32 {
        let max_plants = (available_space / space_per_plant) as i32;
        if goals.maximize_yield {
            max_plants
        } else {
            (max_plants as f32 * 0.7) as i32 // 70% for diversity
        }
    }

    fn find_companion_plants(
        &self,
        existing_recommendations: &[PlantingRecommendation],
        herba: &Option<HerbaPlant>,
    ) -> Vec<i32> {
        // Simplified companion plant finding
        Vec::new()
    }

    fn generate_planting_notes(
        &self,
        plant: &Plant,
        herba: &Option<HerbaPlant>,
        goals: &OptimizationGoals,
    ) -> String {
        let mut notes = Vec::new();

        if let Some(h) = herba {
            if let Some(water_req) = &h.water_requirements {
                notes.push(format!("Water: {}", water_req));
            }
            if let Some(sun_req) = &h.sun_requirements {
                notes.push(format!("Sun: {}", sun_req));
            }
        }

        if goals.maximize_companion_planting {
            notes.push("Consider companion planting".to_string());
        }

        notes.join("; ")
    }

    fn is_suitable_for_succession(&self, recommendation: &PlantingRecommendation) -> bool {
        // Check if plant is suitable for succession planting (fast-growing crops)
        recommendation.harvest_date.signed_duration_since(recommendation.planting_date).num_days() < 60
    }

    fn calculate_succession_interval(&self, recommendation: &PlantingRecommendation) -> i32 {
        // Calculate interval between succession plantings
        let days_to_harvest = recommendation.harvest_date.signed_duration_since(recommendation.planting_date).num_days();
        (days_to_harvest / 3) as i32 // Plant every 1/3 of growing period
    }
}

// Supporting structures
#[derive(Debug, Clone)]
struct GrowingSpaceAnalysis {
    total_area: f32,
    available_area: f32,
    is_indoor: bool,
    soil_quality: SoilQuality,
    sun_exposure: SunExposure,
    water_access: WaterAccess,
}

#[derive(Debug, Clone)]
struct PlantWithHerba {
    plant: Plant,
    herba_plant: Option<HerbaPlant>,
}

#[derive(Debug, Clone)]
enum SoilQuality {
    Poor,
    Medium,
    Good,
    Excellent,
}

#[derive(Debug, Clone)]
enum SunExposure {
    FullShade,
    PartialShade,
    PartialSun,
    FullSun,
}

#[derive(Debug, Clone)]
enum WaterAccess {
    Poor,
    Good,
    Excellent,
}

struct PlacementGrid {
    width: i32,
    height: i32,
    used: Vec<Vec<bool>>,
}

impl PlacementGrid {
    fn new(width: i32, height: i32) -> Self {
        Self {
            width,
            height,
            used: vec![vec![false; height as usize]; width as usize],
        }
    }

    fn mark_used(&mut self, x: i32, y: i32, size: i32) {
        for i in x..(x + size).min(self.width) {
            for j in y..(y + size).min(self.height) {
                if i >= 0 && j >= 0 && i < self.width && j < self.height {
                    self.used[i as usize][j as usize] = true;
                }
            }
        }
    }
}
